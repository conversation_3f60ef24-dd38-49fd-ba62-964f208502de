'use client'

import { useEffect, useRef } from 'react'
import { createClient } from '@/lib/supabase/client'
import { useAuth } from '@/hooks/useAuth'
import { useFocusManager } from '@/hooks/useFocusManager'

/**
 * Component to handle session refresh and prevent stale auth state
 */
export function SessionRefresh() {
  const { user, loading } = useAuth()
  const supabase = createClient()
  const refreshIntervalRef = useRef<NodeJS.Timeout | null>(null)
  const lastRefreshRef = useRef<number>(0)
  const focusManager = useFocusManager()

  useEffect(() => {
    // Don't start refresh cycle if still loading or no user
    if (loading || !user) {
      return
    }

    const startRefreshCycle = () => {
      // Clear any existing interval
      if (refreshIntervalRef.current) {
        clearInterval(refreshIntervalRef.current)
      }

      // Refresh session every 5 minutes
      refreshIntervalRef.current = setInterval(async () => {
        const now = Date.now()
        
        // Prevent too frequent refreshes
        if (now - lastRefreshRef.current < 60000) { // 1 minute minimum
          return
        }

        try {
          console.log('Refreshing session...')
          const { data, error } = await supabase.auth.refreshSession()
          
          if (error) {
            console.error('Session refresh error:', error)
          } else {
            console.log('Session refreshed successfully')
            lastRefreshRef.current = now
          }
        } catch (error) {
          console.error('Session refresh failed:', error)
        }
      }, 5 * 60 * 1000) // 5 minutes
    }

    startRefreshCycle()

    // Register with focus manager for coordinated session refresh
    const unregister = focusManager.registerRecoveryAction({
      id: 'session-refresh',
      priority: 2, // Medium priority - after connection but before queries
      condition: () => {
        const now = Date.now()
        return focusManager.wasHiddenLong() && (now - lastRefreshRef.current > 5 * 60 * 1000)
      },
      action: async () => {
        console.log('Focus recovery: refreshing session after long absence...')
        await supabase.auth.refreshSession()
        lastRefreshRef.current = Date.now()
      }
    })

    return () => {
      if (refreshIntervalRef.current) {
        clearInterval(refreshIntervalRef.current)
      }
      unregister()
    }
  }, [user, loading, supabase.auth, focusManager])

  // This component doesn't render anything
  return null
}

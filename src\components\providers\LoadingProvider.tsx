'use client'

import React, { createContext, useContext, useState, useCallback, useRef } from 'react'
import { LoadingPage } from '@/components/ui/loading'
import { useFocusManager } from '@/hooks/useFocusManager'
import { useGlobalLoadingState, useLoadingState } from '@/hooks/useLoadingCoordinator'

interface LoadingContextType {
  isLoading: boolean
  loadingText: string
  showLoading: (text?: string) => void
  hideLoading: () => void
  withLoading: <T>(promise: Promise<T>, text?: string) => Promise<T>
}

const LoadingContext = createContext<LoadingContextType | undefined>(undefined)

export function LoadingProvider({ children }: { children: React.ReactNode }) {
  const globalLoading = useGlobalLoadingState()
  const loadingState = useLoadingState('global-loading')
  const loadingTimeoutRef = useRef<NodeJS.Timeout | null>(null)

  const showLoading = useCallback((text?: string) => {
    // Clear any existing timeout
    if (loadingTimeoutRef.current) {
      clearTimeout(loadingTimeoutRef.current)
    }

    loadingState.showLoading(text || 'جاري التحميل...', 5) // Medium priority
  }, [loadingState])

  const hideLoading = useCallback(() => {
    // Clear timeout when hiding loading
    if (loadingTimeoutRef.current) {
      clearTimeout(loadingTimeoutRef.current)
      loadingTimeoutRef.current = null
    }

    loadingState.hideLoading()
  }, [loadingState])

  const withLoading = useCallback(async <T,>(promise: Promise<T>, text?: string): Promise<T> => {
    showLoading(text)

    // Add timeout to prevent stuck loading
    const timeoutPromise = new Promise<never>((_, reject) => {
      loadingTimeoutRef.current = setTimeout(() => {
        reject(new Error('Loading timeout'))
      }, 10000) // 10 second timeout
    })

    try {
      const result = await Promise.race([promise, timeoutPromise])
      return result
    } catch (error) {
      console.error('Loading error:', error)
      throw error
    } finally {
      hideLoading()
    }
  }, [showLoading, hideLoading])

  const value: LoadingContextType = {
    isLoading: globalLoading.isLoading,
    loadingText: globalLoading.loadingText,
    showLoading,
    hideLoading,
    withLoading
  }

  return (
    <LoadingContext.Provider value={value}>
      {children}
      {globalLoading.isLoading && (
        <div className="fixed inset-0 z-50 bg-background/80 backdrop-blur-sm">
          <LoadingPage text={globalLoading.loadingText} />
        </div>
      )}
    </LoadingContext.Provider>
  )
}

export function useLoading() {
  const context = useContext(LoadingContext)
  if (context === undefined) {
    throw new Error('useLoading must be used within a LoadingProvider')
  }
  return context
}

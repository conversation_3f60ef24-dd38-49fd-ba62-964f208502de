'use client'

import { useEffect, useRef, useCallback } from 'react'

interface FocusRecoveryAction {
  id: string
  action: () => void | Promise<void>
  priority: number // Lower number = higher priority
  condition?: () => boolean // Optional condition to check before running
}

interface FocusManagerState {
  isRecovering: boolean
  lastFocusTime: number
  lastBlurTime: number
  wasHiddenLong: boolean
}

class FocusManager {
  private static instance: FocusManager
  private actions: Map<string, FocusRecoveryAction> = new Map()
  private state: FocusManagerState = {
    isRecovering: false,
    lastFocusTime: Date.now(),
    lastBlurTime: 0,
    wasHiddenLong: false
  }
  private recoveryTimeout: NodeJS.Timeout | null = null
  private listeners: Set<() => void> = new Set()

  static getInstance(): FocusManager {
    if (!FocusManager.instance) {
      FocusManager.instance = new FocusManager()
    }
    return FocusManager.instance
  }

  private constructor() {
    if (typeof document !== 'undefined') {
      document.addEventListener('visibilitychange', this.handleVisibilityChange)
      window.addEventListener('focus', this.handleWindowFocus)
      window.addEventListener('blur', this.handleWindowBlur)
    }
  }

  private handleVisibilityChange = () => {
    const now = Date.now()
    
    if (document.visibilityState === 'visible') {
      this.state.lastFocusTime = now
      this.state.wasHiddenLong = now - this.state.lastBlurTime > 30000 // 30 seconds
      
      // Debounce recovery actions
      if (this.recoveryTimeout) {
        clearTimeout(this.recoveryTimeout)
      }
      
      this.recoveryTimeout = setTimeout(() => {
        this.executeRecoveryActions()
      }, 500) // 500ms debounce
      
    } else {
      this.state.lastBlurTime = now
      this.state.wasHiddenLong = false
      
      // Cancel any pending recovery
      if (this.recoveryTimeout) {
        clearTimeout(this.recoveryTimeout)
        this.recoveryTimeout = null
      }
    }
    
    // Notify listeners
    this.listeners.forEach(listener => listener())
  }

  private handleWindowFocus = () => {
    // Additional focus handling if needed
  }

  private handleWindowBlur = () => {
    // Additional blur handling if needed
  }

  private async executeRecoveryActions() {
    if (this.state.isRecovering) {
      console.log('Recovery already in progress, skipping...')
      return
    }

    this.state.isRecovering = true
    console.log('Starting coordinated focus recovery...')

    try {
      // Sort actions by priority
      const sortedActions = Array.from(this.actions.values())
        .sort((a, b) => a.priority - b.priority)

      // Execute actions sequentially to avoid conflicts
      for (const action of sortedActions) {
        try {
          // Check condition if provided
          if (action.condition && !action.condition()) {
            console.log(`Skipping recovery action ${action.id} - condition not met`)
            continue
          }

          console.log(`Executing recovery action: ${action.id}`)
          await action.action()
          
          // Small delay between actions to prevent conflicts
          await new Promise(resolve => setTimeout(resolve, 100))
          
        } catch (error) {
          console.warn(`Recovery action ${action.id} failed:`, error)
          // Continue with other actions even if one fails
        }
      }
    } finally {
      this.state.isRecovering = false
      console.log('Focus recovery completed')
    }
  }

  registerAction(action: FocusRecoveryAction) {
    this.actions.set(action.id, action)
    
    return () => {
      this.actions.delete(action.id)
    }
  }

  addListener(listener: () => void) {
    this.listeners.add(listener)
    
    return () => {
      this.listeners.delete(listener)
    }
  }

  getState(): Readonly<FocusManagerState> {
    return { ...this.state }
  }

  isVisible(): boolean {
    return document.visibilityState === 'visible'
  }

  wasHiddenLong(): boolean {
    return this.state.wasHiddenLong
  }

  isRecovering(): boolean {
    return this.state.isRecovering
  }
}

export function useFocusManager() {
  const manager = useRef<FocusManager>()
  
  if (!manager.current) {
    manager.current = FocusManager.getInstance()
  }

  const registerRecoveryAction = useCallback((action: FocusRecoveryAction) => {
    return manager.current!.registerAction(action)
  }, [])

  const addStateListener = useCallback((listener: () => void) => {
    return manager.current!.addListener(listener)
  }, [])

  const getState = useCallback(() => {
    return manager.current!.getState()
  }, [])

  const isVisible = useCallback(() => {
    return manager.current!.isVisible()
  }, [])

  const wasHiddenLong = useCallback(() => {
    return manager.current!.wasHiddenLong()
  }, [])

  const isRecovering = useCallback(() => {
    return manager.current!.isRecovering()
  }, [])

  return {
    registerRecoveryAction,
    addStateListener,
    getState,
    isVisible,
    wasHiddenLong,
    isRecovering
  }
}

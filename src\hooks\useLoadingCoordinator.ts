'use client'

import React, { useEffect, useRef, useCallback } from 'react'
import { useFocusManager } from './useFocusManager'

interface LoadingState {
  id: string
  text: string
  priority: number // Lower number = higher priority
  timestamp: number
}

class LoadingCoordinator {
  private static instance: LoadingCoordinator
  private activeLoaders: Map<string, LoadingState> = new Map()
  private listeners: Set<(state: LoadingState | null) => void> = new Set()
  private currentState: LoadingState | null = null

  static getInstance(): LoadingCoordinator {
    if (!LoadingCoordinator.instance) {
      LoadingCoordinator.instance = new LoadingCoordinator()
    }
    return LoadingCoordinator.instance
  }

  private constructor() {}

  private updateCurrentState() {
    const loaders = Array.from(this.activeLoaders.values())
    
    if (loaders.length === 0) {
      this.currentState = null
    } else {
      // Sort by priority (lower number = higher priority), then by timestamp (newer first)
      loaders.sort((a, b) => {
        if (a.priority !== b.priority) {
          return a.priority - b.priority
        }
        return b.timestamp - a.timestamp
      })
      
      this.currentState = loaders[0]
    }

    // Notify all listeners
    this.listeners.forEach(listener => listener(this.currentState))
  }

  addLoader(state: LoadingState) {
    this.activeLoaders.set(state.id, state)
    this.updateCurrentState()
    
    return () => {
      this.removeLoader(state.id)
    }
  }

  removeLoader(id: string) {
    this.activeLoaders.delete(id)
    this.updateCurrentState()
  }

  addListener(listener: (state: LoadingState | null) => void) {
    this.listeners.add(listener)
    
    // Immediately call with current state
    listener(this.currentState)
    
    return () => {
      this.listeners.delete(listener)
    }
  }

  getCurrentState(): LoadingState | null {
    return this.currentState
  }

  hasActiveLoaders(): boolean {
    return this.activeLoaders.size > 0
  }

  clearAllLoaders() {
    this.activeLoaders.clear()
    this.updateCurrentState()
  }
}

export function useLoadingCoordinator() {
  const coordinator = useRef<LoadingCoordinator>()
  const focusManager = useFocusManager()
  
  if (!coordinator.current) {
    coordinator.current = LoadingCoordinator.getInstance()
  }

  const addLoader = useCallback((
    id: string, 
    text: string, 
    priority: number = 5
  ) => {
    // Don't add loaders during focus recovery unless it's high priority
    if (focusManager.isRecovering() && priority > 2) {
      console.log(`Skipping loader ${id} - focus recovery in progress`)
      return () => {} // Return empty cleanup function
    }

    const state: LoadingState = {
      id,
      text,
      priority,
      timestamp: Date.now()
    }

    return coordinator.current!.addLoader(state)
  }, [focusManager])

  const removeLoader = useCallback((id: string) => {
    coordinator.current!.removeLoader(id)
  }, [])

  const addListener = useCallback((listener: (state: LoadingState | null) => void) => {
    return coordinator.current!.addListener(listener)
  }, [])

  const getCurrentState = useCallback(() => {
    return coordinator.current!.getCurrentState()
  }, [])

  const hasActiveLoaders = useCallback(() => {
    return coordinator.current!.hasActiveLoaders()
  }, [])

  const clearAllLoaders = useCallback(() => {
    coordinator.current!.clearAllLoaders()
  }, [])

  // Clear all loaders when focus is lost for a long time
  useEffect(() => {
    const unregister = focusManager.addStateListener(() => {
      if (focusManager.wasHiddenLong()) {
        console.log('Clearing all loaders after long absence')
        clearAllLoaders()
      }
    })

    return unregister
  }, [focusManager, clearAllLoaders])

  return {
    addLoader,
    removeLoader,
    addListener,
    getCurrentState,
    hasActiveLoaders,
    clearAllLoaders
  }
}

// Hook for components that want to show loading state
export function useGlobalLoadingState() {
  const [currentState, setCurrentState] = React.useState<LoadingState | null>(null)
  const coordinator = useLoadingCoordinator()

  React.useEffect(() => {
    const unregister = coordinator.addListener(setCurrentState)
    return unregister
  }, [coordinator])

  return {
    isLoading: !!currentState,
    loadingText: currentState?.text || '',
    loadingPriority: currentState?.priority || 0
  }
}

// Hook for components that want to register a loading state
export function useLoadingState(id: string, defaultText: string = 'جاري التحميل...') {
  const coordinator = useLoadingCoordinator()
  const cleanupRef = useRef<(() => void) | null>(null)

  const showLoading = useCallback((text?: string, priority?: number) => {
    // Clean up previous loader if exists
    if (cleanupRef.current) {
      cleanupRef.current()
    }

    cleanupRef.current = coordinator.addLoader(
      id,
      text || defaultText,
      priority
    )
  }, [coordinator, id, defaultText])

  const hideLoading = useCallback(() => {
    if (cleanupRef.current) {
      cleanupRef.current()
      cleanupRef.current = null
    }
  }, [])

  // Cleanup on unmount
  React.useEffect(() => {
    return () => {
      if (cleanupRef.current) {
        cleanupRef.current()
      }
    }
  }, [])

  return {
    showLoading,
    hideLoading
  }
}

'use client'

import React, { createContext, useContext, useState, useCallback, useEffect } from 'react'
import { usePathname, useRouter } from 'next/navigation'
import { LoadingPage } from '@/components/ui/loading'
import { useFocusManager } from '@/hooks/useFocusManager'
import { useLoadingState, useGlobalLoadingState } from '@/hooks/useLoadingCoordinator'

interface NavigationContextType {
  isNavigating: boolean
  navigationText: string
  startNavigation: (text?: string) => void
  finishNavigation: () => void
  navigateWithLoading: (path: string, text?: string) => void
}

const NavigationContext = createContext<NavigationContextType | undefined>(undefined)

export function NavigationProvider({ children }: { children: React.ReactNode }) {
  const pathname = usePathname()
  const router = useRouter()
  const [previousPathname, setPreviousPathname] = useState(pathname)
  const focusManager = useFocusManager()
  const navigationLoading = useLoadingState('navigation')
  const globalLoading = useGlobalLoadingState()

  const startNavigation = useCallback((text?: string) => {
    // Don't start navigation if focus recovery is in progress
    if (focusManager.isRecovering()) {
      console.log('Skipping navigation - focus recovery in progress')
      return
    }

    navigationLoading.showLoading(text || 'جاري تحميل الصفحة...', 3) // High priority
  }, [focusManager, navigationLoading])

  const finishNavigation = useCallback(() => {
    navigationLoading.hideLoading()
  }, [navigationLoading])

  const navigateWithLoading = useCallback((path: string, text?: string) => {
    if (path === pathname) return // Don't navigate to same page

    // Don't navigate if focus recovery is in progress
    if (focusManager.isRecovering()) {
      console.log('Delaying navigation - focus recovery in progress')
      setTimeout(() => navigateWithLoading(path, text), 1000)
      return
    }

    startNavigation(text)

    // Add a small delay to show loading state
    setTimeout(() => {
      router.push(path)
    }, 100)
  }, [pathname, router, startNavigation, focusManager])

  // Auto-finish navigation when pathname changes
  useEffect(() => {
    if (pathname !== previousPathname) {
      // Small delay to ensure smooth transition
      const timer = setTimeout(() => {
        finishNavigation()
        setPreviousPathname(pathname)
      }, 300)

      return () => clearTimeout(timer)
    }
  }, [pathname, previousPathname, finishNavigation])

  // Safety timeout to prevent stuck loading
  useEffect(() => {
    if (isNavigating) {
      const timeout = setTimeout(() => {
        console.warn('Navigation timeout - forcing finish')
        finishNavigation()
      }, 5000) // 5 second timeout

      return () => clearTimeout(timeout)
    }
  }, [isNavigating, finishNavigation])

  const value: NavigationContextType = {
    isNavigating: globalLoading.isLoading && globalLoading.loadingPriority <= 3,
    navigationText: globalLoading.loadingText,
    startNavigation,
    finishNavigation,
    navigateWithLoading
  }

  return (
    <NavigationContext.Provider value={value}>
      {children}
      {/* Navigation loading is now handled by the global loading coordinator */}
    </NavigationContext.Provider>
  )
}

export function useNavigation() {
  const context = useContext(NavigationContext)
  if (context === undefined) {
    throw new Error('useNavigation must be used within a NavigationProvider')
  }
  return context
}
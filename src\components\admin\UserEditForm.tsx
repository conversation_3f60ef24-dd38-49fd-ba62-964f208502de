'use client'

import { useState, useEffect } from 'react'
import { useRout<PERSON> } from 'next/navigation'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { UserAvatar } from '@/components/ui/user-avatar'
import { Separator } from '@/components/ui/separator'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from '@/components/ui/alert-dialog'
import { ArrowLeft, Edit, Trash2, Mail, Phone, Calendar, User, Shield, Save, X } from 'lucide-react'
import { UserRole, RoleLevel } from '@/lib/supabase'
import { getRoleArabicName, getRoleBadgeVariant, getAssignableRoles } from '@/lib/roles'
import { useAuth } from '@/hooks/useAuth'
import { toast } from '@/hooks/use-toast'

type Profile = {
  id: string
  email: string
  full_name: string | null
  phone: string | null
  role: UserRole
  role_level: RoleLevel
  created_at: string
  updated_at: string
}

interface UserEditFormProps {
  userId: string
  onCancel: () => void
  onSuccess: () => void
  showBackButton?: boolean
}

export function UserEditForm({ userId, onCancel, onSuccess, showBackButton = false }: UserEditFormProps) {
  const router = useRouter()
  const { profile: currentUserProfile } = useAuth()
  const [user, setUser] = useState<Profile | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState('')
  const [success, setSuccess] = useState('')
  const [isDeleting, setIsDeleting] = useState(false)

  const [editForm, setEditForm] = useState({
    full_name: '',
    phone: '',
    role: 'sales_employee' as UserRole,
    salary: 3000
  })

  // Get assignable roles based on current user's role
  const assignableRoles = getAssignableRoles(currentUserProfile?.role || 'sales_employee')

  const fetchUser = async () => {
    try {
      setLoading(true)
      setError('')

      // Try admin API first, fall back to general profile API
      let response = await fetch(`/api/admin/users/${userId}`)
      let result = await response.json()

      // If admin API fails due to permissions, try general profile API
      if (!response.ok && response.status === 403) {
        response = await fetch(`/api/profile/${userId}`)
        result = await response.json()
      }

      if (!response.ok) {
        setError(result.error || 'حدث خطأ في جلب بيانات المستخدم')
        return
      }

      setUser(result.user)
      setEditForm({
        full_name: result.user.full_name || '',
        phone: result.user.phone || '',
        role: result.user.role,
        salary: result.user.salary || 3000
      })
    } catch (err) {
      setError('حدث خطأ غير متوقع')
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchUser()
  }, [userId])

  const handleUpdateUser = async () => {
    if (!user) return
    
    setLoading(true)
    setError('')
    setSuccess('')

    try {
      const response = await fetch('/api/admin/update-user', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          userId: user.id,
          full_name: editForm.full_name,
          phone: editForm.phone,
          role: editForm.role,
          salary: editForm.salary
        })
      })

      const result = await response.json()

      if (!response.ok) {
        toast.error('خطأ في تحديث المستخدم: ' + result.error)
        return
      }

      toast.success('تم تحديث بيانات المستخدم بنجاح')

      // Call onSuccess after a short delay
      setTimeout(() => {
        onSuccess()
      }, 1000)
      
    } catch (err) {
      toast.error('حدث خطأ غير متوقع')
    } finally {
      setLoading(false)
    }
  }

  const handleDeleteUser = async () => {
    if (!user) return
    
    setIsDeleting(true)
    setError('')

    try {
      const response = await fetch('/api/admin/delete-user', {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ userId: user.id })
      })

      const result = await response.json()

      if (!response.ok) {
        toast.error('خطأ في حذف المستخدم: ' + result.error)
        return
      }

      toast.success('تم حذف المستخدم بنجاح')
      // Redirect to users list after successful deletion
      router.push('/dashboard/users')
    } catch (err) {
      toast.error('حدث خطأ غير متوقع')
    } finally {
      setIsDeleting(false)
    }
  }

  const formatDate = (dateString: string) => {
    // Use a consistent format to avoid hydration mismatches
    const date = new Date(dateString)
    return date.toLocaleDateString('ar-SA', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
      timeZone: 'UTC' // Use UTC to ensure consistency between server and client
    })
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center space-y-4">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
          <p className="text-muted-foreground">جاري تحميل بيانات المستخدم...</p>
        </div>
      </div>
    )
  }

  if (error && !user) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center space-y-4">
          <p className="text-destructive">{error}</p>
          <div className="flex gap-2 justify-center">
            <Button onClick={fetchUser}>
              إعادة المحاولة
            </Button>
            <Button variant="outline" onClick={onCancel}>
              العودة
            </Button>
          </div>
        </div>
      </div>
    )
  }

  if (!user) return null

  return (
    <div className="space-y-6" dir="rtl">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          {showBackButton && (
            <Button variant="ghost" size="sm" onClick={() => router.back()}>
              <ArrowLeft className="h-4 w-4 ml-2" />
              العودة
            </Button>
          )}
          <div>
            <h2 className="text-3xl font-bold">تعديل المستخدم</h2>
            <p className="text-muted-foreground mt-2">
              تعديل بيانات المستخدم
            </p>
          </div>
        </div>
        
        <div className="flex gap-2">
          <AlertDialog>
            <AlertDialogTrigger asChild>
              <Button variant="destructive">
                <Trash2 className="h-4 w-4 ml-2" />
                حذف
              </Button>
            </AlertDialogTrigger>
            <AlertDialogContent className="text-right" dir="rtl">
              <AlertDialogHeader className="text-right">
                <AlertDialogTitle className="text-right">تأكيد الحذف</AlertDialogTitle>
                <AlertDialogDescription className="text-right">
                  هل أنت متأكد من حذف هذا المستخدم؟ هذا الإجراء لا يمكن التراجع عنه وسيتم حذف جميع بيانات المستخدم نهائياً.
                </AlertDialogDescription>
              </AlertDialogHeader>
              <AlertDialogFooter className="flex-row-reverse gap-2">
                <AlertDialogAction 
                  onClick={handleDeleteUser}
                  disabled={isDeleting}
                  className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
                >
                  {isDeleting ? 'جاري الحذف...' : 'حذف نهائياً'}
                </AlertDialogAction>
                <AlertDialogCancel>إلغاء</AlertDialogCancel>
              </AlertDialogFooter>
            </AlertDialogContent>
          </AlertDialog>
        </div>
      </div>



      {/* User Profile Card */}
      <Card>
        <CardHeader>
          <div className="flex items-start gap-6">
            <UserAvatar
              src=""
              alt={user.full_name || 'مستخدم'}
              name={user.full_name}
              size="xl"
            />
            
            <div className="flex-1 space-y-2">
              <div className="flex items-center gap-3">
                <h3 className="text-2xl font-bold">
                  {user.full_name || 'غير محدد'}
                </h3>
                <Badge variant={getRoleBadgeVariant(user.role)}>
                  {getRoleArabicName(user.role)}
                </Badge>
              </div>
              
              <div className="flex items-center gap-2 text-muted-foreground">
                <Mail className="h-4 w-4" />
                <span>{user.email}</span>
              </div>
              
              {user.phone && (
                <div className="flex items-center gap-2 text-muted-foreground">
                  <Phone className="h-4 w-4" />
                  <span>{user.phone}</span>
                </div>
              )}
            </div>
          </div>
        </CardHeader>
      </Card>

      {/* Edit Form */}
      <div className="grid gap-6 md:grid-cols-2">
        {/* Basic Information */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Edit className="h-5 w-5" />
              تعديل المعلومات الأساسية
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="edit_full_name" className="text-right block">الاسم الكامل</Label>
              <Input
                id="edit_full_name"
                value={editForm.full_name}
                onChange={(e) => setEditForm({ ...editForm, full_name: e.target.value })}
                placeholder="أدخل الاسم الكامل"
                className="text-right"
                dir="rtl"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="edit_phone" className="text-right block">رقم الهاتف</Label>
              <Input
                id="edit_phone"
                type="tel"
                value={editForm.phone}
                onChange={(e) => setEditForm({ ...editForm, phone: e.target.value })}
                placeholder="أدخل رقم الهاتف"
                className="text-right"
                dir="rtl"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="edit_salary" className="text-right block">الراتب</Label>
              <Input
                id="edit_salary"
                type="number"
                value={editForm.salary}
                onChange={(e) => setEditForm({ ...editForm, salary: Number(e.target.value) || 3000 })}
                placeholder="أدخل الراتب"
                className="text-right"
                dir="rtl"
                min="0"
                step="0.01"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="edit_role" className="text-right block">الدور</Label>
              <Select value={editForm.role} onValueChange={(value: UserRole) => setEditForm({ ...editForm, role: value })} dir="rtl">
                <SelectTrigger className="text-right" dir="rtl">
                  <SelectValue placeholder="اختر الدور" />
                </SelectTrigger>
                <SelectContent className="text-right" dir="rtl">
                  {assignableRoles.map((role) => (
                    <SelectItem key={role} value={role} className="text-right">
                      {getRoleArabicName(role)}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="flex justify-start gap-2 pt-4" dir="rtl">
              <Button onClick={handleUpdateUser} disabled={loading}>
                <Save className="h-4 w-4 ml-2" />
                {loading ? 'جاري الحفظ...' : 'حفظ التغييرات'}
              </Button>
              <Button variant="outline" onClick={onCancel}>
                <X className="h-4 w-4 ml-2" />
                إلغاء
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Account Information */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Calendar className="h-5 w-5" />
              معلومات الحساب
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <Label className="text-sm text-muted-foreground">تاريخ إنشاء الحساب</Label>
              <p className="text-sm font-medium">{formatDate(user.created_at)}</p>
            </div>

            <Separator />

            <div>
              <Label className="text-sm text-muted-foreground">آخر تحديث</Label>
              <p className="text-sm font-medium">{formatDate(user.updated_at)}</p>
            </div>

            <Separator />

            <div>
              <Label className="text-sm text-muted-foreground">معرف المستخدم</Label>
              <p className="text-xs font-mono bg-muted p-2 rounded">{user.id}</p>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}

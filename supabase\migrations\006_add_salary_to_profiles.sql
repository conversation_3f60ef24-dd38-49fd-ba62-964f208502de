-- Add salary field to profiles table
ALTER TABLE public.profiles 
ADD COLUMN salary DECIMAL(10,2) DEFAULT 3000.00;

-- Update the handle_new_user function to include salary with default value
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
    INSERT INTO public.profiles (id, email, full_name, phone, role, role_level, salary)
    VALUES (
        NEW.id, 
        NEW.email, 
        NEW.raw_user_meta_data->>'full_name',
        NEW.raw_user_meta_data->>'phone',
        'sales_employee',
        4,
        3000.00
    );
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

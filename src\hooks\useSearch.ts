'use client'

import { useState, useEffect, useCallback, useRef } from 'react'
import { SearchResponse, SearchResult, SearchType } from '@/types/search'
import { useDebounce } from './useDebounce'
import { useFocusManager } from './useFocusManager'

interface UseSearchOptions {
  debounceMs?: number
  minQueryLength?: number
  limit?: number
  type?: SearchType
}

interface UseSearchReturn {
  query: string
  setQuery: (query: string) => void
  results: SearchResult[]
  categories: SearchResponse['categories']
  loading: boolean
  error: string | null
  total: number
  isOpen: boolean
  setIsOpen: (open: boolean) => void
  clearSearch: () => void
  selectedIndex: number
  setSelectedIndex: (index: number) => void
  handleKeyDown: (e: React.KeyboardEvent) => void
}

export function useSearch(options: UseSearchOptions = {}): UseSearchReturn {
  const {
    debounceMs = 300,
    minQueryLength = 2,
    limit = 10,
    type = 'all'
  } = options

  const [query, setQuery] = useState('')
  const [results, setResults] = useState<SearchResult[]>([])
  const [categories, setCategories] = useState<SearchResponse['categories']>({})
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [total, setTotal] = useState(0)
  const [isOpen, setIsOpen] = useState(false)
  const [selectedIndex, setSelectedIndex] = useState(-1)

  const debouncedQuery = useDebounce(query, debounceMs)
  const abortControllerRef = useRef<AbortController | null>(null)
  const focusManager = useFocusManager()

  const performSearch = useCallback(async (searchQuery: string) => {
    if (!searchQuery || searchQuery.length < minQueryLength) {
      setResults([])
      setCategories({})
      setTotal(0)
      setLoading(false)
      return
    }

    // Don't search if focus recovery is in progress
    if (focusManager.isRecovering()) {
      console.log('Delaying search - focus recovery in progress')
      setTimeout(() => performSearch(searchQuery), 1000)
      return
    }

    // Cancel previous request
    if (abortControllerRef.current) {
      abortControllerRef.current.abort()
    }

    // Create new abort controller
    abortControllerRef.current = new AbortController()

    setLoading(true)
    setError(null)

    try {
      const params = new URLSearchParams({
        q: searchQuery,
        type,
        limit: limit.toString()
      })

      const response = await fetch(`/api/search?${params}`, {
        signal: abortControllerRef.current.signal
      })

      if (!response.ok) {
        throw new Error('فشل في البحث')
      }

      const data: SearchResponse = await response.json()

      setResults(data.results)
      setCategories(data.categories)
      setTotal(data.total)
      setSelectedIndex(-1) // Reset selection
      
    } catch (err: any) {
      if (err.name !== 'AbortError') {
        setError(err.message || 'حدث خطأ في البحث')
        setResults([])
        setCategories({})
        setTotal(0)
      }
    } finally {
      setLoading(false)
    }
  }, [minQueryLength, type, limit, focusManager])

  // Perform search when debounced query changes
  useEffect(() => {
    performSearch(debouncedQuery)
  }, [debouncedQuery, performSearch])

  // Open search results when query is entered and has results
  useEffect(() => {
    if (query.length >= minQueryLength && (results.length > 0 || loading)) {
      setIsOpen(true)
    } else if (query.length < minQueryLength) {
      setIsOpen(false)
    }
  }, [query, minQueryLength, results.length, loading])

  // Clear search when browser loses focus for a long time
  useEffect(() => {
    const unregister = focusManager.addStateListener(() => {
      if (focusManager.wasHiddenLong() && query) {
        console.log('Clearing search after long absence')
        clearSearch()
      }
    })

    return unregister
  }, [focusManager, query, clearSearch])

  const clearSearch = useCallback(() => {
    setQuery('')
    setResults([])
    setCategories({})
    setTotal(0)
    setError(null)
    setIsOpen(false)
    setSelectedIndex(-1)
  }, [])

  const handleKeyDown = useCallback((e: React.KeyboardEvent) => {
    if (!isOpen || results.length === 0) return

    switch (e.key) {
      case 'ArrowDown':
        e.preventDefault()
        setSelectedIndex(prev => 
          prev < results.length - 1 ? prev + 1 : prev
        )
        break
      
      case 'ArrowUp':
        e.preventDefault()
        setSelectedIndex(prev => prev > 0 ? prev - 1 : -1)
        break
      
      case 'Enter':
        e.preventDefault()
        if (selectedIndex >= 0 && selectedIndex < results.length) {
          const selectedResult = results[selectedIndex]
          window.location.href = selectedResult.url
        }
        break
      
      case 'Escape':
        e.preventDefault()
        setIsOpen(false)
        setSelectedIndex(-1)
        break
    }
  }, [isOpen, results, selectedIndex])

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (abortControllerRef.current) {
        abortControllerRef.current.abort()
      }
    }
  }, [])

  return {
    query,
    setQuery,
    results,
    categories,
    loading,
    error,
    total,
    isOpen,
    setIsOpen,
    clearSearch,
    selectedIndex,
    setSelectedIndex,
    handleKeyDown
  }
}

'use client'

import { useEffect, useRef, useCallback } from 'react'
import { supabase } from '@/lib/supabase'
import { useFocusManager } from './useFocusManager'

export function useSupabaseConnection() {
  const isReconnectingRef = useRef(false)
  const focusManager = useFocusManager()

  const refreshConnection = useCallback(async () => {
    if (isReconnectingRef.current) {
      console.log('Connection refresh already in progress, skipping...')
      return
    }

    isReconnectingRef.current = true

    try {
      console.log('Refreshing Supabase connection...')
      await supabase.auth.getSession()
      console.log('Supabase connection refreshed successfully')
    } catch (error) {
      console.warn('Failed to refresh Supabase connection:', error)
    } finally {
      isReconnectingRef.current = false
    }
  }, [])

  const handleOnline = useCallback(() => {
    // Network came back online - trigger connection refresh
    if (!isReconnectingRef.current) {
      refreshConnection()
    }
  }, [refreshConnection])

  useEffect(() => {
    // Register with focus manager for coordinated recovery
    const unregister = focusManager.registerRecoveryAction({
      id: 'supabase-connection',
      priority: 1, // High priority - connection should be restored first
      condition: () => focusManager.wasHiddenLong(), // Only refresh if hidden for a while
      action: refreshConnection
    })

    // Listen for network status changes
    window.addEventListener('online', handleOnline)

    return () => {
      unregister()
      window.removeEventListener('online', handleOnline)
    }
  }, [focusManager, refreshConnection, handleOnline])

  // Return a function to manually trigger reconnection
  const forceReconnect = useCallback(() => {
    refreshConnection()
  }, [refreshConnection])

  return { forceReconnect }
}
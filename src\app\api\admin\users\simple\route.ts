import { NextResponse } from 'next/server'
import { createServerClient } from '@supabase/ssr'
import { cookies } from 'next/headers'
import { isSystemAdmin } from '@/lib/roles'

export async function GET() {
  try {
    console.log('Simple users API called')
    
    // Check authentication
    const cookieStore = await cookies()
    
    const supabase = createServerClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          getAll() {
            return cookieStore.getAll()
          },
          setAll(cookiesToSet) {
            try {
              cookiesToSet.forEach(({ name, value, options }) =>
                cookieStore.set(name, value, options)
              )
            } catch {
              // Ignore
            }
          },
        },
      }
    )

    console.log('Checking user authentication...')

    // Check if user is authenticated
    const { data: { user }, error: userError } = await supabase.auth.getUser()

    if (userError || !user) {
      console.log('User not authenticated:', userError?.message)
      return NextResponse.json(
        { error: 'غير مصرح لك بالوصول' },
        { status: 401 }
      )
    }

    console.log('User authenticated:', user.id)

    // Get user profile to check role
    const { data: profile, error: profileError } = await supabase
      .from('profiles')
      .select('role')
      .eq('id', user.id)
      .single()

    if (profileError || !profile) {
      console.log('Profile error:', profileError?.message)
      return NextResponse.json(
        { error: 'خطأ في جلب بيانات المستخدم' },
        { status: 500 }
      )
    }

    if (!isSystemAdmin(profile.role)) {
      console.log('User not system admin:', profile.role)
      return NextResponse.json(
        { error: 'غير مصرح لك بالوصول لهذه الصفحة' },
        { status: 403 }
      )
    }

    console.log('User is system admin, fetching users...')

    // Get all users (simple query without relationships)
    const { data: users, error } = await supabase
      .from('profiles')
      .select(`
        id,
        email,
        full_name,
        phone,
        role,
        role_level,
        area_id,
        team_id,
        salary,
        created_at,
        updated_at
      `)
      .order('created_at', { ascending: false })

    if (error) {
      console.error('Error fetching users:', error)
      return NextResponse.json(
        { error: `خطأ في جلب المستخدمين: ${error.message}` },
        { status: 500 }
      )
    }

    console.log(`Successfully fetched ${users?.length || 0} users`)

    return NextResponse.json({ users: users || [] })

  } catch (error: any) {
    console.error('Unexpected error in users API:', error)
    return NextResponse.json(
      { error: error.message || 'حدث خطأ غير متوقع' },
      { status: 500 }
    )
  }
}

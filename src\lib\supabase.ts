import { createClient } from '@supabase/supabase-js'

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!

export const supabase = createClient(supabaseUrl, supabaseAnonKey)

// Role hierarchy types
export type UserRole = 'system_admin' | 'area_manager' | 'team_manager' | 'sales_employee'

export type RoleLevel = 1 | 2 | 3 | 4

export interface RoleInfo {
  role: UserRole
  level: RoleLevel
  arabicName: string
  description: string
  permissions: string[]
}

export const ROLE_HIERARCHY: Record<UserRole, RoleInfo> = {
  system_admin: {
    role: 'system_admin',
    level: 1,
    arabicName: 'مدير النظام',
    description: 'يدير النظام بالكامل ولديه صلاحيات كاملة على جميع الوظائف',
    permissions: [
      'إدارة النظام',
      'إدارة المستخدمين',
      'إنشاء المستخدمين',
      'تعديل المستخدمين',
      'حذف المستخدمين',
      'عرض التقارير',
      'إدارة المشاريع',
      'إدارة الفرق',
      'إدارة المبيعات'
    ]
  },
  area_manager: {
    role: 'area_manager',
    level: 2,
    arabicName: 'مدير منطقة',
    description: 'يدير منطقة جغرافية ويشرف على مديري الفرق وموظفي المبيعات',
    permissions: [
      'إدارة الفرق',
      'إدارة المبيعات',
      'عرض التقارير',
      'تعديل المستخدمين',
      'إدارة المشاريع'
    ]
  },
  team_manager: {
    role: 'team_manager',
    level: 3,
    arabicName: 'مدير فريق',
    description: 'يدير فريق مبيعات ويشرف على أداء موظفي المبيعات',
    permissions: [
      'إدارة المبيعات',
      'عرض التقارير',
      'تعديل المستخدمين'
    ]
  },
  sales_employee: {
    role: 'sales_employee',
    level: 4,
    arabicName: 'موظف مبيعات',
    description: 'ينفذ عمليات المبيعات ويدير بياناته الشخصية فقط',
    permissions: [
      'إدارة المبيعات'
    ]
  }
}

export type Database = {
  public: {
    Tables: {
      profiles: {
        Row: {
          id: string
          email: string
          full_name: string | null
          phone: string | null
          role: UserRole
          role_level: RoleLevel
          area_id: string | null
          team_id: string | null
          salary: number
          created_at: string
          updated_at: string
        }
        Insert: {
          id: string
          email: string
          full_name?: string | null
          phone?: string | null
          role?: UserRole
          role_level?: RoleLevel
          area_id?: string | null
          team_id?: string | null
          salary?: number
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          email?: string
          full_name?: string | null
          phone?: string | null
          role?: UserRole
          role_level?: RoleLevel
          area_id?: string | null
          team_id?: string | null
          salary?: number
          created_at?: string
          updated_at?: string
        }
      }
      areas: {
        Row: {
          id: string
          name: string
          description: string | null
          manager_id: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          name: string
          description?: string | null
          manager_id?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          name?: string
          description?: string | null
          manager_id?: string | null
          created_at?: string
          updated_at?: string
        }
      }
      teams: {
        Row: {
          id: string
          name: string
          description: string | null
          area_id: string
          manager_id: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          name: string
          description?: string | null
          area_id: string
          manager_id?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          name?: string
          description?: string | null
          area_id?: string
          manager_id?: string | null
          created_at?: string
          updated_at?: string
        }
      }
      packages: {
        Row: {
          id: string
          name: string
          description: string | null
          price: number
          is_active: boolean
          created_by: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          name: string
          description?: string | null
          price: number
          is_active?: boolean
          created_by?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          name?: string
          description?: string | null
          price?: number
          is_active?: boolean
          created_by?: string | null
          created_at?: string
          updated_at?: string
        }
      }
      daily_closings: {
        Row: {
          id: string
          user_id: string
          closing_date: string
          attendance_submitted: boolean
          sales_submitted: boolean
          departure_submitted: boolean
          total_sales_amount: number
          cash_delivered: number
          advances_amount: number
          price_breaks_amount: number
          cash_confirmed: boolean
          deficit_amount: number
          notes: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          user_id: string
          closing_date: string
          attendance_submitted?: boolean
          sales_submitted?: boolean
          departure_submitted?: boolean
          total_sales_amount?: number
          cash_delivered?: number
          advances_amount?: number
          price_breaks_amount?: number
          cash_confirmed?: boolean
          deficit_amount?: number
          notes?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          closing_date?: string
          attendance_submitted?: boolean
          sales_submitted?: boolean
          departure_submitted?: boolean
          total_sales_amount?: number
          cash_delivered?: number
          advances_amount?: number
          price_breaks_amount?: number
          cash_confirmed?: boolean
          deficit_amount?: number
          notes?: string | null
          created_at?: string
          updated_at?: string
        }
      }
      attendance_records: {
        Row: {
          id: string
          daily_closing_id: string
          check_in_time: string
          check_out_time: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          daily_closing_id: string
          check_in_time: string
          check_out_time?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          daily_closing_id?: string
          check_in_time?: string
          check_out_time?: string | null
          created_at?: string
          updated_at?: string
        }
      }
      sales_records: {
        Row: {
          id: string
          daily_closing_id: string
          package_id: string
          quantity: number
          unit_price: number
          total_amount: number
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          daily_closing_id: string
          package_id: string
          quantity: number
          unit_price: number
          total_amount: number
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          daily_closing_id?: string
          package_id?: string
          quantity?: number
          unit_price?: number
          total_amount?: number
          created_at?: string
          updated_at?: string
        }
      }
      packages: {
        Row: {
          id: string
          name: string
          description: string | null
          price: number
          is_active: boolean
          created_by: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          name: string
          description?: string | null
          price: number
          is_active?: boolean
          created_by?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          name?: string
          description?: string | null
          price?: number
          is_active?: boolean
          created_by?: string | null
          created_at?: string
          updated_at?: string
        }
      }
      tickets: {
        Row: {
          id: string
          title: string
          description: string
          status: 'open' | 'in_progress' | 'closed'
          priority: 'low' | 'medium' | 'high'
          created_by: string
          assigned_to: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          title: string
          description: string
          status?: 'open' | 'in_progress' | 'closed'
          priority?: 'low' | 'medium' | 'high'
          created_by: string
          assigned_to?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          title?: string
          description?: string
          status?: 'open' | 'in_progress' | 'closed'
          priority?: 'low' | 'medium' | 'high'
          created_by?: string
          assigned_to?: string | null
          created_at?: string
          updated_at?: string
        }
      }
      ticket_replies: {
        Row: {
          id: string
          ticket_id: string
          user_id: string
          message: string
          created_at: string
        }
        Insert: {
          id?: string
          ticket_id: string
          user_id: string
          message: string
          created_at?: string
        }
        Update: {
          id?: string
          ticket_id?: string
          user_id?: string
          message?: string
          created_at?: string
        }
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      has_role_level_or_higher: {
        Args: { required_level: number }
        Returns: boolean
      }
      get_user_role_level: {
        Args: {}
        Returns: number
      }
      get_user_role: {
        Args: {}
        Returns: string
      }
    }
    Enums: {
      [_ in never]: never
    }
  }
}
